import 'dart:io';
import 'package:audio_service/audio_service.dart';
import 'package:sluqe/services/record/audio_service_handler.dart';

class DebugAudioService {
  static Future<void> testInitialization() async {
    print('=== DEBUG: Testing Audio Service Initialization ===');
    
    print('Platform: ${Platform.operatingSystem}');
    print('Is iOS: ${Platform.isIOS}');
    print('Is Android: ${Platform.isAndroid}');
    
    try {
      print('Attempting to initialize AudioService...');
      
      final audioHandler = await AudioService.init(
        builder: () => RecordingAudioHandler(),
        config: AudioServiceConfig(
          androidNotificationChannelId: 'io.boet.sluqe.debug',
          androidNotificationChannelName: 'Debug Audio Recording',
          androidNotificationChannelDescription: 'Debug recording audio in background',
          androidNotificationOngoing: false, // Start with simple config
          androidStopForegroundOnPause: false,
        ),
      );
      
      print('✅ AudioService initialized successfully!');
      print('AudioHandler type: ${audioHandler.runtimeType}');
      
      // Test basic functionality
      await audioHandler.prepare();
      print('✅ AudioHandler prepared successfully!');
      
      return;
      
    } catch (e) {
      print('❌ AudioService initialization failed: $e');
      print('Error type: ${e.runtimeType}');
      
      if (e.toString().contains('androidNotificationOngoing')) {
        print('🔧 Trying alternative configuration...');
        
        try {
          final audioHandler = await AudioService.init(
            builder: () => RecordingAudioHandler(),
            config: AudioServiceConfig(
              androidNotificationChannelId: 'io.boet.sluqe.debug2',
              androidNotificationChannelName: 'Debug Audio Recording 2',
              androidNotificationChannelDescription: 'Debug recording audio in background 2',
            ),
          );
          
          print('✅ AudioService initialized with minimal config!');
          await audioHandler.prepare();
          print('✅ AudioHandler prepared successfully!');
          
        } catch (e2) {
          print('❌ Alternative configuration also failed: $e2');
        }
      }
    }
    
    print('=== DEBUG: Audio Service Test Complete ===');
  }
}
