import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:sluqe/core/utils/task_handler.dart';
import 'package:sluqe/services/record/ios_live_activity_service.dart';
import 'package:sluqe/services/record/media_notification_service.dart';

class ForegroundService {
  static Future<void> initialize() async {
    await IOSLiveActivityService.initialize();
    await MediaNotificationService.instance.initialize();

    FlutterForegroundTask.init(
      androidNotificationOptions: AndroidNotificationOptions(
        channelId: 'record_channel',
        channelName: 'Audio Recording',
        channelDescription: 'Keep audio recorder running',
        channelImportance: NotificationChannelImportance.HIGH,
        priority: NotificationPriority.HIGH,
        enableVibration: false,
        playSound: false,
      ),
      iosNotificationOptions: IOSNotificationOptions(
        showNotification: false, // Disable basic notification, use audio_service instead
        playSound: false,
      ),
      foregroundTaskOptions: ForegroundTaskOptions(
        eventAction: ForegroundTaskEventAction.repeat(5000), // Reduced frequency
        autoRunOnBoot: true,
        allowWakeLock: true,
        allowWifiLock: true,
      ),
    );
  }

  static Future<ServiceRequestResult> start(String formattedDuration, {Function()? onStopPressed}) async {
    await IOSLiveActivityService.startRecordingActivity(
      duration: formattedDuration,
      onStopPressed: () {
        sendDataToTask({'action': 'stop'});
        onStopPressed?.call();
      },
    );

    // Start media notification service for iOS-style notifications
    await MediaNotificationService.instance.startRecordingNotification(
      onStopPressed: () {
        sendDataToTask({'action': 'stop'});
        onStopPressed?.call();
      },
    );

    if (await FlutterForegroundTask.isRunningService == true) {
      return FlutterForegroundTask.restartService();
    } else {
      // Start foreground task without notification (audio_service handles it)
      return FlutterForegroundTask.startService(
        notificationTitle: '🔴 Recording Audio',
        notificationText: 'Duration: $formattedDuration • Tap to return to app',
        notificationInitialRoute: '/home',
        notificationButtons: [], // Remove buttons, audio_service handles them
        callback: startCallback,
      );
    }
  }

  static Future<void> updateNotification(String duration) async {
    // Update Live Activity silently
    await IOSLiveActivityService.updateRecordingActivity(duration);

    // Update media notification with new duration
    final durationParts = duration.split(':');
    if (durationParts.length >= 2) {
      final minutes = int.tryParse(durationParts[0]) ?? 0;
      final seconds = int.tryParse(durationParts[1]) ?? 0;
      final durationObj = Duration(minutes: minutes, seconds: seconds);
      MediaNotificationService.instance.updateRecordingDuration(durationObj);
    }

    // DO NOT UPDATE FOREGROUND SERVICE NOTIFICATION - it causes popup notifications
    // The initial notification will stay persistent without updates
  }

  static Future<void> stop() async {
    await IOSLiveActivityService.stopRecordingActivity();
    await MediaNotificationService.instance.stopRecordingNotification();
    FlutterForegroundTask.stopService();
  }

  static void addTaskDataCallback(Function(Object) callback) {
    FlutterForegroundTask.addTaskDataCallback(callback);
  }

  static void removeTaskDataCallback(Function(Object) callback) {
    FlutterForegroundTask.removeTaskDataCallback(callback);
  }

  static void sendDataToTask(Map<String, dynamic> data) {
    FlutterForegroundTask.sendDataToTask(data);
  }
}
