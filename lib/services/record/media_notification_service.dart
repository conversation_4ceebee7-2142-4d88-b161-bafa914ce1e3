import 'dart:async';
import 'dart:io';
import 'package:audio_service/audio_service.dart';
import 'package:sluqe/services/record/audio_service_handler.dart';

class MediaNotificationService {
  static MediaNotificationService? _instance;
  static MediaNotificationService get instance => _instance ??= MediaNotificationService._();
  
  MediaNotificationService._();

  RecordingAudioHandler? _audioHandler;
  bool _isInitialized = false;
  Function()? _onStopCallback;

  /// Initialize the audio service
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Only initialize on supported platforms
    if (!isAvailable) {
      print('MediaNotificationService: Platform not supported');
      return;
    }

    try {
      // Try minimal configuration first for iOS
      _audioHandler = await AudioService.init(
        builder: () => RecordingAudioHandler(),
        config: AudioServiceConfig(
          androidNotificationChannelId: 'io.boet.sluqe.recording',
          androidNotificationChannelName: 'Audio Recording',
          androidNotificationChannelDescription: 'Recording audio in background',
          // Minimal config for iOS compatibility
        ),
      );

      _isInitialized = true;
      print('MediaNotificationService initialized successfully');
    } catch (e) {
      print('Failed to initialize MediaNotificationService: $e');
      print('This is normal on iOS simulator or if audio permissions are not granted');
      _isInitialized = false;

      // Try a simpler configuration as fallback
      try {
        _audioHandler = await AudioService.init(
          builder: () => RecordingAudioHandler(),
          config: AudioServiceConfig(
            androidNotificationChannelId: 'io.boet.sluqe.recording',
            androidNotificationChannelName: 'Audio Recording',
            androidNotificationChannelDescription: 'Recording audio in background',
            androidNotificationOngoing: false, // Fallback: disable ongoing notification
            androidStopForegroundOnPause: false,
          ),
        );
        _isInitialized = true;
        print('MediaNotificationService initialized with fallback config');
      } catch (fallbackError) {
        print('Fallback initialization also failed: $fallbackError');
        _isInitialized = false;
      }
    }
  }

  /// Start recording notification
  Future<void> startRecordingNotification({
    required Function() onStopPressed,
  }) async {
    if (!_isInitialized || _audioHandler == null) {
      print('MediaNotificationService not initialized');
      return;
    }

    _onStopCallback = onStopPressed;

    try {
      await _audioHandler!.startRecording(
        onStopPressed: () {
          _onStopCallback?.call();
        },
      );
      print('Recording notification started');
    } catch (e) {
      print('Failed to start recording notification: $e');
    }
  }

  /// Update recording duration in notification
  void updateRecordingDuration(Duration duration) {
    if (!_isInitialized || _audioHandler == null) return;

    try {
      _audioHandler!.updateRecordingDuration(duration);
    } catch (e) {
      print('Failed to update recording duration: $e');
    }
  }

  /// Stop recording notification
  Future<void> stopRecordingNotification() async {
    if (!_isInitialized || _audioHandler == null) return;

    try {
      await _audioHandler!.stopRecording();
      print('Recording notification stopped');
    } catch (e) {
      print('Failed to stop recording notification: $e');
    }
  }

  /// Check if recording notification is active
  bool get isRecording => _audioHandler?.isRecording ?? false;

  /// Get current recording duration
  Duration get recordingDuration => _audioHandler?.recordingDuration ?? Duration.zero;

  /// Format duration for display
  String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '$hours:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  /// Dispose resources
  Future<void> dispose() async {
    if (_audioHandler != null) {
      await _audioHandler!.stopRecording();
    }
    _audioHandler = null;
    _isInitialized = false;
    _onStopCallback = null;
  }

  /// Check if the service is available (iOS specific checks)
  bool get isAvailable {
    // Audio service is available on both iOS and Android
    // but we can add platform-specific checks if needed
    return Platform.isIOS || Platform.isAndroid;
  }

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;
}
