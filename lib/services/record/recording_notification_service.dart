import 'dart:async';
import 'dart:io';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:sluqe/services/record/audio_service_manager.dart';
import 'package:sluqe/services/record/simulator_notification_service.dart';

class RecordingNotificationService {
  static RecordingNotificationService? _instance;
  static RecordingNotificationService get instance => _instance ??= RecordingNotificationService._();
  
  RecordingNotificationService._();

  bool _isRecording = false;
  Duration _recordingDuration = Duration.zero;
  Function()? _onStopCallback;
  Timer? _updateTimer;

  /// Initialize the notification service
  Future<void> initialize() async {
    print('RecordingNotificationService: Initializing...');

    // For iOS simulator, use local notifications
    if (Platform.isIOS) {
      // Always initialize simulator notification service for iOS
      await SimulatorNotificationService.instance.initialize();

      // Also try to initialize audio service for real devices
      await AudioServiceManager.instance.initialize();
    }

    print('RecordingNotificationService: Initialization complete');
  }

  /// Start recording notification
  Future<void> startRecordingNotification({
    required Function() onStopPressed,
  }) async {
    _onStopCallback = onStopPressed;
    _isRecording = true;
    _recordingDuration = Duration.zero;

    print('RecordingNotificationService: Starting recording notification...');

    // For iOS, try both audio service and simulator notification
    bool audioServiceStarted = false;
    bool simulatorNotificationStarted = false;

    if (Platform.isIOS) {
      // Try audio service first (works on real devices)
      if (AudioServiceManager.instance.isInitialized) {
        audioServiceStarted = await AudioServiceManager.instance.startRecordingNotification(
          onStopPressed: onStopPressed,
        );
        print('RecordingNotificationService: Audio service started: $audioServiceStarted');
      }

      // Always start simulator notification (works on simulator and as backup)
      if (SimulatorNotificationService.instance.isInitialized) {
        await SimulatorNotificationService.instance.startRecordingNotification(
          onStopPressed: onStopPressed,
        );
        simulatorNotificationStarted = true;
        print('RecordingNotificationService: Simulator notification started: $simulatorNotificationStarted');
      }
    }

    // If neither started, use enhanced foreground notification
    if (!audioServiceStarted && !simulatorNotificationStarted) {
      await _startEnhancedForegroundNotification();
    }

    // Start the update timer
    _startUpdateTimer();
  }

  /// Start enhanced foreground notification with recording time
  Future<void> _startEnhancedForegroundNotification() async {
    print('RecordingNotificationService: Starting enhanced foreground notification');
    
    final formattedDuration = _formatDuration(_recordingDuration);
    
    if (await FlutterForegroundTask.isRunningService == true) {
      await FlutterForegroundTask.restartService();
    } else {
      await FlutterForegroundTask.startService(
        notificationTitle: '🔴 Recording Audio',
        notificationText: 'Duration: $formattedDuration • Tap to return to app',
        notificationInitialRoute: '/home',
        notificationButtons: [
          NotificationButton(id: 'stop', text: 'STOP RECORDING'),
        ],
        callback: _foregroundTaskCallback,
      );
    }
  }

  /// Update recording duration
  void updateRecordingDuration(Duration duration) {
    if (!_isRecording) return;

    _recordingDuration = duration;

    // Update all active notification services
    if (Platform.isIOS) {
      // Update audio service if available
      if (AudioServiceManager.instance.isInitialized) {
        AudioServiceManager.instance.updateRecordingDuration(duration);
      }

      // Update simulator notification if available
      if (SimulatorNotificationService.instance.isInitialized) {
        SimulatorNotificationService.instance.updateRecordingDuration(duration);
      }
    } else {
      // Update foreground notification
      _updateForegroundNotification();
    }
  }

  /// Update foreground notification with new duration
  void _updateForegroundNotification() {
    // Don't update too frequently to avoid notification spam
    // The timer will handle regular updates
  }

  /// Start update timer for foreground notifications
  void _startUpdateTimer() {
    _updateTimer?.cancel();
    _updateTimer = Timer.periodic(Duration(seconds: 5), (timer) {
      if (!_isRecording) {
        timer.cancel();
        return;
      }
      
      // Update foreground notification every 5 seconds
      if (!AudioServiceManager.instance.isInitialized) {
        final formattedDuration = _formatDuration(_recordingDuration);
        FlutterForegroundTask.updateService(
          notificationTitle: '🔴 Recording Audio',
          notificationText: 'Duration: $formattedDuration • Tap to return to app',
        );
      }
    });
  }

  /// Stop recording notification
  Future<void> stopRecordingNotification() async {
    _isRecording = false;
    _updateTimer?.cancel();
    _updateTimer = null;

    print('RecordingNotificationService: Stopping recording notification');

    // Stop all notification services
    if (Platform.isIOS) {
      // Stop audio service if running
      if (AudioServiceManager.instance.isInitialized) {
        await AudioServiceManager.instance.stopRecordingNotification();
      }

      // Stop simulator notification if running
      if (SimulatorNotificationService.instance.isInitialized) {
        await SimulatorNotificationService.instance.stopRecordingNotification();
      }
    }

    // Stop foreground service
    FlutterForegroundTask.stopService();
  }

  /// Format duration for display
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '$hours:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  /// Check if currently recording
  bool get isRecording => _isRecording;

  /// Get current recording duration
  Duration get recordingDuration => _recordingDuration;

  /// Check if service is available
  bool get isAvailable => Platform.isIOS || Platform.isAndroid;

  /// Check if service is initialized
  bool get isInitialized => true; // Always available as fallback
}

/// Foreground task callback for handling notification button presses
@pragma('vm:entry-point')
void _foregroundTaskCallback() {
  FlutterForegroundTask.setTaskHandler(_RecordingTaskHandler());
}

/// Send data to task for communication
void sendDataToTask(Map<String, dynamic> data) {
  FlutterForegroundTask.sendDataToTask(data);
}

class _RecordingTaskHandler extends TaskHandler {
  @override
  void onNotificationButtonPressed(String id) {
    super.onNotificationButtonPressed(id);
    
    if (id == 'stop') {
      // Send stop signal to main app
      FlutterForegroundTask.sendDataToMain({'action': 'stop_recording'});
    }
  }

  @override
  Future<void> onStart(DateTime timestamp, TaskStarter starter) async {
    print('Recording task started');
  }

  @override
  void onRepeatEvent(DateTime timestamp) {
    // Handle periodic events if needed
  }

  @override
  Future<void> onDestroy(DateTime timestamp, bool isTimeout) async {
    print('Recording task destroyed');
  }
}
