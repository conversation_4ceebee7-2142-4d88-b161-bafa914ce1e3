import 'dart:async';
import 'dart:io';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class SimulatorNotificationService {
  static SimulatorNotificationService? _instance;
  static SimulatorNotificationService get instance => _instance ??= SimulatorNotificationService._();
  
  SimulatorNotificationService._();

  FlutterLocalNotificationsPlugin? _flutterLocalNotificationsPlugin;
  bool _isInitialized = false;
  bool _isRecording = false;
  Duration _recordingDuration = Duration.zero;
  Timer? _updateTimer;

  static const int _notificationId = 12345;
  static const String _channelId = 'recording_channel';
  static const String _channelName = 'Recording Notifications';

  /// Initialize the notification service for simulator
  Future<void> initialize() async {
    if (_isInitialized) return;

    print('SimulatorNotificationService: Initializing for iOS Simulator...');

    _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

    // iOS initialization settings
    const iosInitializationSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: false,
    );

    const initializationSettings = InitializationSettings(
      iOS: iosInitializationSettings,
    );

    await _flutterLocalNotificationsPlugin!.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationResponse,
    );

    // Request permissions
    await _flutterLocalNotificationsPlugin!
        .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
        ?.requestPermissions(
          alert: true,
          badge: true,
          sound: false,
        );

    _isInitialized = true;
    print('SimulatorNotificationService: Initialized successfully');
  }

  /// Handle notification button responses
  void _onNotificationResponse(NotificationResponse response) {
    print('SimulatorNotificationService: Notification tapped');
    // For now, just tapping the notification will return to app
    // In the future, we can add action buttons when the API supports it better
  }

  /// Start recording notification with live updates
  Future<void> startRecordingNotification({
    required Function() onStopPressed,
  }) async {
    if (!_isInitialized) {
      print('SimulatorNotificationService: Not initialized');
      return;
    }

    _onStopCallback = onStopPressed;
    _isRecording = true;
    _recordingDuration = Duration.zero;

    print('SimulatorNotificationService: Starting recording notification');

    // Show initial notification
    await _showRecordingNotification();

    // Start update timer
    _startUpdateTimer();
  }

  /// Show/update the recording notification
  Future<void> _showRecordingNotification() async {
    if (!_isInitialized || !_isRecording) return;

    final formattedDuration = _formatDuration(_recordingDuration);

    // iOS notification details
    const iosNotificationDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: false,
      presentSound: false,
    );

    const notificationDetails = NotificationDetails(
      iOS: iosNotificationDetails,
    );

    try {
      await _flutterLocalNotificationsPlugin!.show(
        _notificationId,
        '🔴 Recording Audio',
        'Duration: $formattedDuration • Return to app to stop recording',
        notificationDetails,
        payload: 'recording_notification',
      );

      print('SimulatorNotificationService: Notification updated - $formattedDuration');
    } catch (e) {
      print('SimulatorNotificationService: Failed to show notification: $e');
    }
  }



  /// Start timer to update notification every second
  void _startUpdateTimer() {
    _updateTimer?.cancel();
    _updateTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (!_isRecording) {
        timer.cancel();
        return;
      }

      _recordingDuration = _recordingDuration + Duration(seconds: 1);
      _showRecordingNotification();
    });
  }

  /// Update recording duration
  void updateRecordingDuration(Duration duration) {
    if (!_isRecording) return;
    
    _recordingDuration = duration;
    _showRecordingNotification();
  }

  /// Stop recording notification
  Future<void> stopRecordingNotification() async {
    _isRecording = false;
    _updateTimer?.cancel();
    _updateTimer = null;

    if (_isInitialized) {
      await _flutterLocalNotificationsPlugin!.cancel(_notificationId);
      print('SimulatorNotificationService: Recording notification stopped');
    }
  }

  /// Format duration for display
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '$hours:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  /// Check if currently recording
  bool get isRecording => _isRecording;

  /// Get current recording duration
  Duration get recordingDuration => _recordingDuration;

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  /// Check if running on simulator
  static bool get isSimulator {
    // This is a simple check - in a real app you might want more sophisticated detection
    return Platform.isIOS; // For now, we'll use this for iOS simulator testing
  }
}
